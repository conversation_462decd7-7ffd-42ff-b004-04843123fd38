
import os
import glob
import pandas as pd
import pyarrow as pa
import pyarrow.ipc as ipc
from PIL import Image
import matplotlib.pyplot as plt
import io

def find_arrow_files(base_dir):
    """Recursively finds all .arrow files in the given directory."""
    pattern = os.path.join(base_dir, '**', '*.arrow')
    return glob.glob(pattern, recursive=True)

def display_record(df, index):
    """Displays a single record (row) from the DataFrame."""
    if index < 0 or index >= len(df):
        print(f"无效索引. 请输入 0 到 {len(df) - 1} 之间的数字.")
        return

    os.system('cls' if os.name == 'nt' else 'clear')
    print(f"--- 查看记录: {index + 1} / {len(df)} ---")
    print("=" * 50)

    row = df.iloc[index]
    image_columns = []

    # 打印文本数据并查找图片列
    for col_name, value in row.items():
        # 假设图片数据存储在字典中，键为 'bytes'
        if isinstance(value, dict) and 'bytes' in value and value['bytes']:
            image_columns.append((col_name, value['bytes']))
        elif isinstance(value, bytes) and value:
             # 有时图片数据直接就是bytes
             image_columns.append((col_name, value))
        else:
            print(f"  {col_name}: {value}")

    # 显示图片
    if not image_columns:
        print("\n[未在本条记录中找到图片]")
    else:
        print(f"\n[在本条记录中找到 {len(image_columns)} 张图片...]")
        # 关闭之前的所有图片窗口
        plt.close('all')

        for i, (col_name, img_bytes) in enumerate(image_columns):
            try:
                image = Image.open(io.BytesIO(img_bytes))
                plt.figure(i + 1, figsize=(8, 6))
                plt.imshow(image)
                # 使用英文标题避免字体问题
                plt.title(f'Image: {col_name} (Record {index + 1})', fontsize=12)
                plt.axis('off')
            except Exception as e:
                print(f"无法显示图片 '{col_name}': {e}")

        # 只显示一次
        if image_columns:
            plt.tight_layout()
            plt.show()


def main():
    """Main function to run the interactive viewer."""
    # Build path relative to the script's location
    script_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.abspath(os.path.join(script_dir, '..'))
    base_dir = os.path.join(project_root, 'MMMU', 'MMMU___mmmu_pro')
    
    print("正在搜索 .arrow 文件...")
    files = find_arrow_files(base_dir)

    if not files:
        print(f"在 '{base_dir}' 目录中未找到 .arrow 文件.")
        return

    print("找到以下文件:")
    for i, f in enumerate(files):
        print(f"  {i + 1}: {f}")

    # 选择文件
    while True:
        try:
            choice = int(input(f"\n请选择要查看的文件 (1-{len(files)}): "))
            if 1 <= choice <= len(files):
                file_path = files[choice - 1]
                break
            else:
                print("无效选择.")
        except ValueError:
            print("请输入一个数字.")

    # 加载数据
    try:
        print(f"正在加载 {file_path}...")
        # 尝试不同的读取方法
        try:
            # 方法1: 作为Arrow IPC文件读取
            with open(file_path, 'rb') as f:
                reader = ipc.open_stream(f)
                table = reader.read_all()
        except:
            try:
                # 方法2: 作为Arrow文件读取
                reader = ipc.open_file(file_path)
                table = reader.read_all()
            except:
                # 方法3: 尝试用memory map
                with pa.memory_map(file_path, 'r') as source:
                    reader = ipc.open_stream(source)
                    table = reader.read_all()

        df = table.to_pandas()
        print("加载完成.")
        print(f"数据形状: {df.shape}")
        print(f"列名: {list(df.columns)}")
    except Exception as e:
        print(f"加载文件失败: {e}")
        return

    # 交互式查看循环
    current_index = 0
    while True:
        display_record(df, current_index)
        
        prompt = "\n操作: (n) 下一个, (p) 上一个, (g) 跳转, (q) 退出 -> "
        user_input = input(prompt).lower()

        if user_input == 'n':
            if current_index < len(df) - 1:
                current_index += 1
            else:
                print("已经是最后一条记录.")
        elif user_input == 'p':
            if current_index > 0:
                current_index -= 1
            else:
                print("已经是第一条记录.")
        elif user_input == 'g':
            try:
                target_index = int(input(f"请输入要跳转的行号 (1-{len(df)}): ")) - 1
                if 0 <= target_index < len(df):
                    current_index = target_index
                else:
                    print("无效行号.")
            except ValueError:
                print("请输入一个有效的数字.")
        elif user_input == 'q':
            print("退出.")
            break
        else:
            print("无效输入.")

if __name__ == "__main__":
    print("欢迎使用 MMMU Arrow 文件交互式查看器!")
    print("-----------------------------------------")
    print("在使用前，请确保已安装所需库:")
    print("pip install pandas pyarrow matplotlib Pillow")
    print("-----------------------------------------")
    main()
