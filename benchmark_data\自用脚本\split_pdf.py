import os
import argparse
from PyPDF2 import Pdf<PERSON><PERSON><PERSON>, PdfWriter

def split_pdf(input_path, start_page, end_page, output_path=None):
    """
    Splits a PDF file from a specified start page to an end page.

    Args:
        input_path (str): The path to the source PDF file.
        start_page (int): The starting page number (1-based, inclusive).
        end_page (int): The ending page number (1-based, inclusive).
        output_path (str, optional): The path to save the new PDF. 
                                     If None, a default name is generated in the input file's directory.
    """
    # --- 1. 验证输入 ---
    if not os.path.exists(input_path):
        print(f"错误: 输入文件不存在: {input_path}")
        return

    if start_page <= 0:
        print("错误: 起始页码必须大于 0。")
        return

    if start_page > end_page:
        print(f"错误: 起始页 ({start_page}) 不能大于结束页 ({end_page})。")
        return

    # --- 2. 确定输出路径 ---
    if output_path is None:
        # 如果未提供输出路径，则自动生成
        input_dir = os.path.dirname(input_path)
        base_name = os.path.splitext(os.path.basename(input_path))[0]
        output_filename = f"{base_name}_p{start_page}-{end_page}.pdf"
        output_path = os.path.join(input_dir, output_filename)
        print(f"未指定输出路径，将自动保存为: {output_path}")


    try:
        # --- 3. 读取 PDF ---
        reader = PdfReader(input_path)
        writer = PdfWriter()
        
        num_pages = len(reader.pages)

        if end_page > num_pages:
            print(f"警告: 结束页 ({end_page}) 大于 PDF 的总页数 ({num_pages})。")
            print(f"将只截取到第 {num_pages} 页。")
            end_page = num_pages

        # --- 4. 提取页面并写入 ---
        # 将用户输入的 1-based 页码转换为 0-based 索引
        start_index = start_page - 1
        
        print(f"正在从 '{os.path.basename(input_path)}' 提取第 {start_page} 到 {end_page} 页...")

        for i in range(start_index, end_page):
            writer.add_page(reader.pages[i])

        # --- 5. 保存新文件 ---
        with open(output_path, 'wb') as output_file:
            writer.write(output_file)
        
        print(f"成功创建新文件: '{output_path}'")

    except Exception as e:
        print(f"处理 PDF 时发生错误: {e}")
        print("请确保文件未损坏且未被加密。")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="一个用于分割 PDF 文件的命令行工具。",
        formatter_class=argparse.RawTextHelpFormatter # 保持帮助信息格式
    )
    
    parser.add_argument(
        "input_pdf", 
        help="要分割的源 PDF 文件路径。"
    )
    parser.add_argument(
        "--start", 
        type=int, 
        required=True, 
        help="分割的起始页码 (包含此页, 从 1 开始)。"
    )
    parser.add_argument(
        "--end", 
        type=int, 
        required=True, 
        help="分割的结束页码 (包含此页)。"
    )
    parser.add_argument(
        "--output", 
        dest="output_pdf",
        default=None,
        help="分割后要保存的新 PDF 文件路径。\n(可选) 如果不提供，将在源文件目录生成一个默认文件名，\n例如 '源文件名_p<起始页>-<结束页>.pdf'。"
    )

    args = parser.parse_args()

    split_pdf(args.input_pdf, args.start, args.end, args.output_pdf)