import pandas as pd
import os
import re

def process_parquet_data(parquet_path, csv_output_path, txt_output_path, subfield_txt_output_path):
    """
    Reads a Parquet file, saves its content to a CSV file,
    and extracts unique keywords from the 'keywords' column into a text file.

    Args:
        parquet_path (str): Path to the input Parquet file.
        csv_output_path (str): Path to save the output CSV file.
        txt_output_path (str): Path to save the output text file with keywords.
    """
    # --- 检查输入文件是否存在 ---
    if not os.path.exists(parquet_path):
        print(f"错误: 输入文件不存在于 {parquet_path}")
        return

    print(f"正在读取 Parquet 文件: {parquet_path}")
    try:
        df = pd.read_parquet(parquet_path)
    except Exception as e:
        print(f"读取 Parquet 文件时出错: {e}")
        print("请确保已安装 'pyarrow' 或 'fastparquet' 库 (pip install pandas pyarrow)")
        return

    # --- 任务 1: 将整个 DataFrame 转换为 CSV 以便查看 ---
    print(f"正在将数据保存为 CSV 文件: {csv_output_path}")
    df.to_csv(csv_output_path, index=False, encoding='utf-8-sig') # 使用 utf-8-sig 确保 Excel 正确显示中文
    print("已成功转换为 CSV 文件。")

    # --- 任务 2: 提取唯一关键词并保存到 txt 文件 ---
    if 'keywords' not in df.columns:
        print("错误: 在 Parquet 文件中未找到 'keywords' 列。")
        return

    print("正在提取唯一关键词...")
    all_keywords = set()
    
    # 'keywords' 列的格式为 "['keyword1' 'keyword2' ...]", 关键词由空格分隔
    # 我们使用正则表达式来提取所有单引号内的内容
    for item in df['keywords'].dropna():
        # re.findall(r"'(.*?)'", item) 会找到所有在单引号之间的文本
        # 例如: "'amines, aromatic' 'organic-chemistry'" -> ['amines, aromatic', 'organic-chemistry']
        keyword_list = re.findall(r"'(.*?)'", str(item))
        if keyword_list:
            all_keywords.update(keyword.strip() for keyword in keyword_list if keyword.strip())

    print(f"已找到 {len(all_keywords)} 个唯一关键词。")

    # 将去重后的关键词保存到文本文件
    print(f"正在将关键词保存到文本文件: {txt_output_path}")
    with open(txt_output_path, 'w', encoding='utf-8') as f:
        # 排序以保证输出文件内容的一致性
        for keyword in sorted(list(all_keywords)):
            f.write(f"{keyword}\n")
    
    print("已成功提取并保存关键词。")

    # --- 任务 3: 提取唯一 subfield 并保存到 txt 文件 ---
    if 'subfield' not in df.columns:
        print("警告: 在 Parquet 文件中未找到 'subfield' 列。")
    else:
        print("正在提取唯一 subfields...")
        all_subfields = df['subfield'].dropna().unique()
        print(f"已找到 {len(all_subfields)} 个唯一 subfield。")

        # 将去重后的 subfield 保存到文本文件
        print(f"正在将 subfields 保存到文本文件: {subfield_txt_output_path}")
        with open(subfield_txt_output_path, 'w', encoding='utf-8') as f:
            for subfield in sorted(all_subfields):
                if subfield: # 确保不写入空字符串
                    f.write(f"{subfield}\n")
        print("已成功提取并保存 subfields。")

    print("\n处理完成。你可以查看以下生成的文件:")
    print(f"1. 完整数据 (CSV): {os.path.abspath(csv_output_path)}")
    print(f"2. 唯一关键词 (TXT): {os.path.abspath(txt_output_path)}")
    if 'subfield' in df.columns and os.path.exists(subfield_txt_output_path):
        print(f"3. 唯一 Subfields (TXT): {os.path.abspath(subfield_txt_output_path)}")


if __name__ == "__main__":
    # --- 文件路径定义 ---
    # Parquet 源文件
    PARQUET_FILE = os.path.join('benchmark_data', 'chembench', 'train-00000-of-00001.parquet')
    # 输出的 CSV 文件
    CSV_OUTPUT_FILE = os.path.join('benchmark_data', 'chembench_data.csv')
    # 输出的关键词 txt 文件
    TXT_OUTPUT_FILE = os.path.join('benchmark_data', 'keywords.txt')
    # 输出的 subfield txt 文件
    SUBFIELD_TXT_OUTPUT_FILE = os.path.join('benchmark_data', 'subfield.txt')

    process_parquet_data(PARQUET_FILE, CSV_OUTPUT_FILE, TXT_OUTPUT_FILE, SUBFIELD_TXT_OUTPUT_FILE)
