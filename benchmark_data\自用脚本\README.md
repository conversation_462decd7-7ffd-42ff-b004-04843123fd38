# 自用脚本说明文档

本文件用于说明该目录下各个 Python 脚本的功能及使用方法。

---

## 1. `read_parquet.py`

### 功能说明

这是一个通用的 Python 脚本，用于读取、分析和交互式查询 Parquet 格式的数据文件。

- **基本信息展示**: 显示 Parquet 文件的基本元数据，包括数据形状、列名、数据类型等。
- **数据预览**: 快速查看文件的前 5 行数据。
- **深度分析**: 对数据进行初步探索，包括计算每列的唯一值数量、统计缺失值等。
- **样本数据导出**: 自动将文件的前 100 行保存为一个同名的 `.csv` 样本文件。
- **交互式查询**: 进入一个交互式模式，允许用户按行号或行号范围实时查询数据。

### 依赖要求
```bash
pip install pandas pyarrow
```

### 如何使用
```bash
python benchmark_data/自用脚本/read_parquet.py <您的 Parquet 文件路径>
```

---

## 2. `process_chembench_data.py`

### 功能说明

此脚本用于处理 `benchmark_data/chembench/` 目录下的 Parquet 格式数据文件 (`train-00000-of-00001.parquet`)。

主要完成三个任务：
1.  **转换为 CSV**: 将完整的 Parquet 数据转换为 `.csv` 文件 (`chembench_data.csv`)，方便人工查看。
2.  **提取关键词**: 提取 `keywords` 列的所有唯一值，并保存到 `keywords.txt`。
3.  **提取子领域**: 提取 `subfield` 列的所有唯一值，并保存到 `subfield.txt`。

### 如何使用

直接在项目根目录 (`benchmark/`) 运行此脚本即可，无需任何参数。

```bash
python benchmark_data/自用脚本/process_chembench_data.py
```
脚本会自动在 `benchmark_data/` 目录下生成 `chembench_data.csv`, `keywords.txt`, 和 `subfield.txt`。

---

## 3. `split_pdf.py`

### 功能说明

这是一个通用的命令行工具，用于分割指定的 PDF 文件，并提取其中任意范围的页面，生成一个新的 PDF 文件。

### 依赖安装
```bash
pip install PyPDF2
```

### 如何使用

通过命令行运行此脚本。输出文件路径现在是可选的。

**命令格式:**
```bash
python benchmark_data/自用脚本/split_pdf.py <输入PDF路径> --start <起始页> --end <结束页> [-o <输出PDF路径>]
```

**参数说明:**
- `<输入PDF路径>`: **(必填)** 要分割的源 PDF 文件。
- `--start <起始页>`: **(必填)** 分割的起始页码 (包含此页, 从 1 开始)。
- `--end <结束页>`: **(必填)** 分割的结束页码 (包含此页)。
- `-o, --output <输出PDF路径>`: **(可选)** 指定输出文件的路径。如果不提供，脚本会在源文件目录下自动生成一个文件名，格式为 `<原文件名>_p<起始页>-<结束页>.pdf`。

**使用示例 1 (指定输出路径):**
```bash
python benchmark_data/自用脚本/split_pdf.py "benchmark_data/有机化学.pdf" --start 10 --end 20 -o "benchmark_data/有机化学_节选.pdf"
```

**使用示例 2 (自动生成文件名):**
```bash
python benchmark_data/自用脚本/split_pdf.py "benchmark_data/有机化学.pdf" --start 10 --end 20
```
> 此命令会自动在 `benchmark_data/` 目录下创建一个名为 `有机化学_p10-20.pdf` 的文件。

**注意:** 如果文件路径中包含空格，请务必用双引号 `"` 将路径括起来。
