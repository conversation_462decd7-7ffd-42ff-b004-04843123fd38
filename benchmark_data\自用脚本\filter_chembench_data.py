import pandas as pd
import os

# --- 用户配置区域 ---
# 请在此列表中填入您想筛选的关键词。
# 匹配逻辑: 只要一行数据的 'keywords' 列包含了列表中的任意一个词，该行就会被选中。
# 示例: KEYWORDS_TO_FILTER = ['organic-chemistry', 'solubility']
# 该列表已经过核对，确保所有关键词与源文件内容完全一致
# 包含了与逆合成分析、反应分析、反应机理相关的关键词
KEYWORDS_TO_FILTER = [
    # --- 逆合成分析 (Retrosynthesis) ---
    'retrosynthesis',              # 逆合成分析
    'synthesis',                   # 合成
    'drug synthesis',              # 药物合成
    'natural product synthesis',   # 天然产物合成
    'directing groups',            # 定位基团
    'molecular scaffolds',         # 分子骨架
    'intermolecular cyclization',  # 分子间环化
    'intermolecular-cyclisation',  # 分子间环化 (cyclization的另一种拼写)
    
    # --- 反应分析 (Reaction Analysis) ---
    'analytical-chemistry',        # 分析化学
    'IR spectroscopy',             # 红外光谱
    'NMR-spectroscopy',            # 核磁共振波谱
    'kinetics',                    # 动力学
    'enzyme kinetics',             # 酶动力学
    'Michaelis-Menten kinetics',   # 米氏动力学
    'PCR Reaction Dynamics',       # PCR反应动力学 (聚合酶链式反应动力学)
    'stoichiometry',               # 化学计量
    'stochiometry',                # 化学计量 (stoichiometry的常见错误拼写)
    'E/Z-selectivity',             # E/Z选择性 (顺反异构选择性)
    'stereoselectivity',           # 立体选择性
    'receptor selectivity',        # 受体选择性
    'area under the curve',        # 曲线下面积 (药代动力学中的一个概念)
    'half-life time',              # 半衰期
    'bateman equation',            # 贝特曼方程 (用于描述连续一级反应中中间产物浓度变化的方程)
    'dose-response',               # 剂量效应关系
    'peptide-sequencing',          # 肽测序
    'signals',                     # 信号 (在光谱分析中指峰)
    'titration',                   # 滴定

    # --- 反应机理 (Reaction Mechanism) ---
    'reaction-mechanism',          # 反应机理
    'sn1',                         # SN1反应 (单分子亲核取代反应)
    'SN2',                         # SN2反应 (双分子亲核取代反应)

    'SN2 reaction mechanism',      # SN2反应机理
    'elimination reaction',        # 消除反应
    'addition',                    # 加成反应
    'conjugate addition',          # 共轭加成
    'substitution',                # 取代反应
    'aromatic substitution',       # 芳香取代反应
    'electrophilic aromatic substitution', # 亲电芳香取代
    'nucleophilic acyl substitution',  # 亲核酰基取代
    'Beckmann rearrangement',      # 贝克曼重排
    'Bischler-Napieralski reaction', # 毕斯勒-纳皮尔斯基反应
    'Friedel-Crafts',              # 傅-克反应 (弗里德尔-克拉夫茨反应)
    'Friedels-Craft',              # 傅-克反应 (Friedel-Crafts的常见错误拼写)
    'click reactions',             # 点击化学反应
    'photocycloaddition',          # 光环加成反应
    'nitration',                   # 硝化反应
    'enolate',                     # 烯醇负离子
    'cation',                      # 阳离子
    'conical intersection',        # 锥形交叉 (光化学反应中的一个概念)
    'enzyme-substrate complex',    # 酶-底物复合物
    'induced-fit',                 # 诱导契合
    'active space',                # 活性空间 (计算化学概念)
    'inductive effect',            # 诱导效应
    'mesomeric effect',            # 共轭效应
    'mesomeric-effect',            # 共轭效应
    'stereochemistry',             # 立体化学
    'chirality',                   # 手性
    'racemic mixture',             # 外消旋混合物
    'ring strain',                 # 环张力
    'Keto-Enol Tautomerism',       # 酮-烯醇互变异构
    'PES',                         # 势能面 (Potential Energy Surface)

    # --- 通用或交叉概念 (General / Cross-Disciplinary) ---
    'Organic Reactions',           # 有机反应
    'chemical reactions',          # 化学反应
    'reactivity',                  # 反应性
    'organic reactivity',          # 有机反应性
    'catalyst',                    # 催化剂
    'acidity',                     # 酸性
    'basicity',                    # 碱性
    'electrophilicity',            # 亲电性
    'nucleophilicity',             # 亲核性
    'stability',                   # 稳定性
    'solubility',                  # 溶解度
    'pka',                         # pKa值 (酸度系数)
    'pH dependency',               # pH依赖性
    'molecular structure',         # 分子结构
    'molecular-structure',         # 分子结构
    'Chemical Bonds',              # 化学键
    'chemical-bonding',            # 化学键合
    'hydrogen bonds',              # 氢键
    'intermolecular interactions', # 分子间相互作用
    'functional groups',           # 官能团
    'functional-groups',           # 官能团
    'named reactions',             # 人名反应
]
# --- 配置结束 ---


def filter_data_by_keywords(input_csv_path, output_csv_path, keywords):
    """
    Filters a CSV file based on a list of keywords and saves the result.

    Args:
        input_csv_path (str): Path to the source CSV file.
        output_csv_path (str): Path to save the filtered CSV file.
        keywords (list): A list of strings to filter by.
    """
    # --- 1. 检查配置和输入文件 ---
    if not keywords:
        print("错误: 关键词列表 'KEYWORDS_TO_FILTER' 为空。")
        print("请先在脚本中填入您想筛选的关键词。")
        return

    if not os.path.exists(input_csv_path):
        print(f"错误: 输入文件不存在: {input_csv_path}")
        return

    print(f"正在读取源文件: {input_csv_path}")
    try:
        df = pd.read_csv(input_csv_path)
    except Exception as e:
        print(f"读取 CSV 文件时出错: {e}")
        return

    # --- 2. 执行筛选 ---
    print(f"将根据以下 {len(keywords)} 个关键词进行筛选: {keywords}")
    
    # 使用正则表达式进行匹配，'|' 代表 '或'
    # 这会匹配任何包含列表中任意一个关键词的行
    filter_pattern = '|'.join(keywords)
    
    # dropna() 确保在筛选前 'keywords' 列中的空值不会导致错误
    filtered_df = df[df['keywords'].str.contains(filter_pattern, case=False, na=False)]

    # --- 3. 保存结果 ---
    if filtered_df.empty:
        print("未找到任何匹配的行。")
    else:
        print(f"已找到 {len(filtered_df)} 行匹配的数据。")
        print(f"正在将结果保存到: {output_csv_path}")
        filtered_df.to_csv(output_csv_path, index=False, encoding='utf-8-sig')
        print("筛选完成，文件已保存。")


if __name__ == "__main__":
    # 定义输入和输出文件路径
    INPUT_FILE = os.path.join('benchmark_data', 'chembench_data.csv')
    OUTPUT_FILE = os.path.join('benchmark_data', 'filtered_chembench_data.csv')

    filter_data_by_keywords(INPUT_FILE, OUTPUT_FILE, KEYWORDS_TO_FILTER)
